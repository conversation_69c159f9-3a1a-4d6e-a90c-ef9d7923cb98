import { defineStore } from 'pinia';
import { acceptInvite, getAcceptList, getDailyLuckyList, getInviteList, getMyStarlight, getTeamStarlightRecord, invite, receiveDailyReward } from '@/api';
import useChannelInfo from '@/stores/modules/use-channel-info';

const useTab1Store = defineStore('tab1', () => {
    const recordTotalValue = ref(0);
    const recordList = ref([]);
    const getTeamStarlightRecordApi = async (payload) => {
        const loading = showLoading();
        const { anchorUid } = storeToRefs(useChannelInfo());
        const [{ code, data }] = await getTeamStarlightRecord({
            ...payload,
            anchorUid: anchorUid.value,
        });
        loading.close();
        recordTotalValue.value = data.totalValue;
        recordList.value = data.list;
        // console.log('recordList', recordList.value);
        if (code === 0) {
            return data;
        }
        return {};
    };
    const inviteList = ref([]);
    const myGetInviteList = ref([]);
    const inviteApi = async (payload) => {
        const loading = showLoading();
        const [{ code, data }] = await invite(payload);
        loading.close();
        if (code === 0) {
            return { code, data };
        }
        return {};
    };

    const acceptInviteApi = async (payload) => {
        const loading = showLoading();
        const [{ code, data }] = await acceptInvite(payload);
        loading.close();
        if (code === 0) {
            return { code, data };
        }
        return {};
    };
    const getInviteListApi = async (payload) => {
        const loading = showLoading();
        const [{ code, data }] = await getInviteList(payload);
        loading.close();
        console.log('getInviteList', data);
        inviteList.value = data.list;
        if (code === 0) {
            return data;
        }
        return {};
    };
    const updateInviteList = (index) => {
        inviteList.value[index].acceptStatus = 1;
    };
    const acceptList = ref([]);
    const getAcceptListApi = async (payload) => {
        const loading = showLoading();
        const [{ code, data }] = await getAcceptList(payload);
        loading.close();
        console.log('getAcceptList', data);
        acceptList.value = data.list;
        if (code === 0) {
            return data;
        }
        return {};
    };
    const myStarlightInfo = ref({});
    const getMyStarlightApi = async (payload) => {
        const { anchorUid } = storeToRefs(useChannelInfo());
        const loading = showLoading();
        const [{ code, data }] = await getMyStarlight({
            anchorUid: anchorUid.value,
        });
        loading.close();

        console.log('getMyStarlight', data);
        myStarlightInfo.value = data;
        if (code === 0) {
            return data;
        }
        return {};
    };
    const receiveDailyRewardApi = async (payload) => {
        const loading = showLoading();
        const [{ code, data }] = await receiveDailyReward(payload);
        loading.close();
        console.log('receiveDailyRewardApi', data);
        if (code === 0) {
            return { code };
        }
        return {};
    };
    const dailyLuckyList = ref([]);
    const getDailyLuckyListApi = async (payload) => {
        const loading = showLoading();
        const [{ code, data }] = await getDailyLuckyList(payload);
        loading.close();
        console.log('getDailyLuckyListApi', data);
        dailyLuckyList.value = data.list;
        if (code === 0) {
            return data;
        }
        return {};
    };
    return {
        getTeamStarlightRecordApi,
        // getMyStarlightApi,
        recordList,
        recordTotalValue,
        inviteList,
        getInviteListApi,
        acceptList,
        getAcceptListApi,
        inviteApi,
        acceptInviteApi,
        updateInviteList,
        getMyStarlightApi,
        myStarlightInfo,
        receiveDailyRewardApi,
        getDailyLuckyListApi,
        dailyLuckyList,

    };
});

export default useTab1Store;
