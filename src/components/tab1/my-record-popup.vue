<template>
    <popup-container
        v-model:show="isShow"
        :close-on-click-overlay="true"
    >
        <div class="my-record-popup">
            <div class="top">
                <img
                    class="avatar"
                    :src="getAvatar(followInitData?.userInfo?.username)" />队伍加成比例: {{ (followInitData?.teamBuff || 0) * 100 }}%
            </div>
            <task-day-select
                v-if="isShow"
                @change-date="changeDate" />
            <div class="task-box">
                <p class="task-title">- 达人基础活跃 -</p>
                <div
                    v-for="(item, index) in recordArr"
                    class="task-item"
                    :class="{ '!mb-[50px]': index === 4 }">
                    <div class="left">
                        <div class="name"><div class="dot"></div>{{ item.task }}{{ index === 5 ? `=${caluTask5Value}超星值` : '' }}</div>
                        <p
                            v-if="item.other"
                            class="gift">
                            {{ item.other }}
                        </p>
                        <p class="progress">今日进度: <span>{{ item?.unit === 'min' ? (Math.floor(recordList?.[index]?.doneCount / 60)) : recordList?.[index]?.doneCount }}{{ item?.unit }}</span></p>
                    </div>
                    <div class="right">
                        <div
                            v-if="recordList?.[index]?.limit"
                            class="label">
                            每日限完成{{ recordList?.[index]?.limit }}次
                        </div>
                        <p class="value">
                            <img
                                class="icon"
                                :src="requireImg('tab1/<EMAIL>')" />+{{ omitValue(recordList?.[index]?.curValue) }}
                        </p>
                    </div>
                </div>
                <p class="task-title title2">- 限定礼物收集 -</p>
                <!-- <div
                    v-for="(item, index) in recordArr.slice(5, 7)"
                    class="task-item">
                    <div class="left">
                        <p class="name">{{ item.task }}</p>
                        <p class="gift">【豆豆礼物名】可在礼物架送出</p>
                        <p class="progress">今日进度: <span>{{ recordList[index]?.completedCount }}</span></p>
                    </div>
                    <div class="right">
                        <div
                            v-if="item.desc"
                            class="label">
                            {{ item.desc }}
                        </div>
                        <p class="value">
                            <img
                                class="icon"
                                :src="requireImg('tab1/<EMAIL>')" /> + {{ recordList[index]?.omitValue(curValue) }}
                        </p>
                    </div>
                </div> -->
            </div>
        </div>
    </popup-container>
</template>

<script setup>
import useTab1Store from './hooks/use-tab1-store';
import useDateSelectStore from './hooks/use-record-date-select-store';
import { recordArr } from './const';
import useInitStore from '@/stores/modules/use-init-store';

const { serverTime, initData, followInitData } = storeToRefs(useInitStore());
const { getTeamStarlightRecordApi } = useTab1Store();
const { recordTotalValue, recordList } = storeToRefs(useTab1Store());
const isShow = ref(false);
const { currentTab } = storeToRefs(useDateSelectStore());
const changeDate = async () => {
    await getTeamStarlightRecordApi({ date: currentTab.value });
    console.log('recordList', recordList.value);
};
const caluTask5Value = computed(() => {
    let num = recordList.value?.[5]?.doneCount;
    if (num <= 400) {
        return 5;
    }
    else if (num <= 800) {
        return 2.5;
    }
    else if (num <= 1200) {
        return 1;
    }
    else return 0.5;
});
useEventBus('my-record-popup').on(({ show = true }) => {
    isShow.value = show;
});
</script>

<style lang="less" scoped>
.my-record-popup {
    .pic-bg(url('@/assets/img/tab1/<EMAIL>'), 375px, 628px);
    padding-top: 5px;
    .top {
        width: 100%;
        display: flex;
        justify-content: flex-end;
        font-size: 16px;
        font-weight: normal;
        text-align: left;
        color: #ffe29c;
        height: 26px;
        align-items: center;
        padding-right: 12px;
        .avatar {
            width: 26px;
            height: 26px;
            border-radius: 50%;
            border: 2px solid #ccc;
            margin-right: 4px;
        }
    }
    .task-box {
        width: 350px;
        height: 554px;
        background: #433d5d;
        border-radius: 10px 10px 0px 0px;
        margin: 0 auto;
        padding-top: 14px;
        position: relative;
    }
    .task-title {
        font-size: 14px;
        font-weight: normal;
        text-align: center;
        color: #d4d4d4;
        margin-bottom: 9px;
    }
    .title2 {
        position: absolute;
        top: 380px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 2;
    }
    .task-item {
        width: 338px;
        height: 57px;
        background: #38334e;
        border-radius: 10px;
        margin: 0 auto 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-left: 10px;
        .left {
            .name {
                font-size: 11px;
                font-weight: 500;
                text-align: left;
                color: #ffebc7;
                margin-bottom: 3px;
                white-space: nowrap;
                display: flex;
                justify-content: flex-start;
                align-items: center;
                .dot {
                    width: 4px;
                    height: 4px;
                    background: #ffebc7;
                    border-radius: 50%;
                }
            }
            .gift {
                font-size: 9px;
                font-weight: 500;
                text-align: left;
                color: #e360ff;
                margin-bottom: 2px;
                padding-left: 10px;
            }
            .progress {
                font-size: 12px;
                font-weight: 500;
                text-align: left;
                color: #e5e5e5;
                padding-left: 10px;
                span {
                    color: #48f0ff;
                }
            }
        }
        .right {
            position: relative;
            height: inherit;
            .value {
                display: flex;
                justify-content: flex-end;
                align-items: center;
                height: inherit;
                margin-top: 8px;
                padding-right: 10px;
                .icon {
                    width: 16px;
                    height: 16px;
                    margin-right: 2px;
                }
                font-size: 14px;
                font-weight: normal;
                text-align: left;
                color: #ffe29c;
            }
            .label {
                .pic-bg(url('@/assets/img/tab1/<EMAIL>'), 80px, 17px);
                font-size: 9px;
                font-weight: 500;
                text-align: center;
                color: #e5e5e5;
                line-height: 17px;
                position: absolute;
                right: 0;
                top: 0;
                z-index: 2;
            }
        }
    }
}
</style>
