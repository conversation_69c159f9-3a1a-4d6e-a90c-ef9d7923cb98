<template>
    <!-- 内容区域 -->
    <div class="relative z-1 flex flex-col items-center pb-100">
        <!-- 标题区域 -->
        <div
            class="bg-default relative h-[49px] w-[350px] flex items-center pl-62"
            :style="{ backgroundImage: `url(${requireImg('tab2/<EMAIL>')})` }"
        >
            <div class="text-[12px] text-[#FFEBC7] leading-[12px]">
                <div class="mb-6 text-13">8月18-25日每晚20:00-22:30 <span class="text-[#F09E56]">开启闪耀官频巡演</span></div>
                <!-- 活动未开始：显示倒计时 -->
                <div v-if="(activityStatus === 0 || activityStatus === 2) && remainTimeStr">
                    距下一场还剩<span class="text-[#F09E56]">{{ remainTimeStr }}</span>
                </div>
                <!-- 活动进行中：显示进行中标识 -->
                <div
                    v-else-if="activityStatus === 1"
                    class="text-[#F09E56]">
                    {{ activityStatusText }}
                </div>
            </div>
        </div>

        <!-- 主播卡片区域 -->
        <div
            class="bg-default relative mb-10 mt-12 h-[325px] w-[375px] flex flex-col items-center"
            :style="{ backgroundImage: `url(${requireImg('tab2/<EMAIL>')})` }"
        >
            <div class="mt-[45px] text-[12px] text-[#FFFFFF]">星光日榜TOP3队伍的学员可登上次日官频巡演</div>
            <!-- 有主播报名的情况 -->
            <div
                v-if="hasAnchors && activityStatus !== 2"
                class="relative w-100%">
                <van-swipe
                    ref="swipeRef"
                    :autoplay="3000"
                    :show-indicators="false"
                    :loop="true"
                    @change="onSwipeChange">
                    <van-swipe-item
                        v-for="(anchor, index) in currentSchedule.anchors"
                        :key="anchor.anchorId"
                        class="w-100% flex justify-center"
                    >
                        <div
                            class="bg-default h-[260.5px] w-[236px] flex flex-col items-center px-5 pt-7 text-center"
                            :style="{ backgroundImage: `url(${requireImg('tab2/<EMAIL>')})` }"
                        >
                            <!-- 主播信息 -->
                            <div class="flex flex-col items-center">
                                <div class="relative inline-block h-[76px] w-[76px]">
                                    <img
                                        class="h-[76px] w-[76px] rounded-full object-cover"
                                        :src="getAvatar(anchor.userInfo.username)"
                                        @click="toPerson(anchor.userInfo.username)" />
                                    <room-status-anchor
                                        v-if="anchor?.channelInfo.channelId"
                                        :cid="anchor?.channelInfo.channelId"
                                        :status="anchor.channelInfo.status"
                                        class="room-status-anchor" />
                                </div>
                                <div class="mt-10 h-[13px] break-all text-left text-[14px] text-[#ffffff] leading-[17.5px]">{{ safeOmitTxt(anchor.userInfo?.nickname) }}</div>
                                <div
                                    class="bg-default line-clamp-3 mt-7 h-[61px] w-[195px] rounded-[10px] p-8 text-center text-10 text-[#DFDFDF]"
                                    :style="{ backgroundImage: `url(${requireImg('tab2/<EMAIL>')})` }"
                                >
                                    {{ anchor.introductionText }}
                                </div>
                            </div>

                            <!-- 关注按钮 (未开启状态) -->
                            <div
                                v-if="activityStatus === 0"
                                class="absolute left-1/2 top-193 h-[32px] w-[121px] translate-x-[-50%]">
                                <img
                                    v-if="!anchor.isFollow"
                                    src="@/assets/img/tab2/<EMAIL>"
                                    class="h-[32px] w-[121px]"
                                    @click.stop="onFollowClick(anchor)">
                                <img
                                    v-else
                                    class="h-[32px] w-[121px]"
                                    src="@/assets/img/tab2/<EMAIL>"
                                    alt="">
                                <div
                                    class="bg-default flex-center absolute right-[-28px] top-[-8px] h-[15px] w-[81.5px] flex text-12 text-[#95390C]"
                                    :style="{ backgroundImage: `url(${requireImg('tab2/<EMAIL>')})` }"
                                >
                                    {{ dayjs.unix(anchor.startTime).format('HH:mm') }}~{{ dayjs.unix(anchor.endTime).format('HH:mm') }}
                                </div>
                            </div>
                            <div
                                v-else
                                class="absolute left-1/2 top-193 h-[32px] w-[121px] translate-x-[-50%]">
                                <img
                                    v-if="initStore.serverTime > anchor.startTime && initStore.serverTime < anchor.endTime"
                                    class="h-[32px] w-[121px]"
                                    src="@/assets/img/tab2/<EMAIL>"
                                    @click="jumpOfficeRoom">
                                <div
                                    v-if="initStore.serverTime > anchor.startTime && initStore.serverTime < anchor.endTime"
                                    class="bg-default flex-center absolute right-[-28px] top-[-8px] h-[15px] w-[81.5px] flex text-12 text-[#95390C]"
                                    :style="{ backgroundImage: `url(${requireImg('tab2/<EMAIL>')})` }"
                                >
                                    当前表演中
                                </div>
                            </div>
                        </div>
                    </van-swipe-item>
                </van-swipe>

                <!-- 手动切换按钮 -->
                <img
                    :src="requireImg('tab2/<EMAIL>')"
                    alt="上一个"
                    class="pointer-events-auto absolute left-25 top-128 h-[30px] w-[30px]"
                    @click="swipePrev" />
                <img
                    :src="requireImg('tab2/<EMAIL>')"
                    alt="下一个"
                    class="pointer-events-auto absolute right-25 top-128 h-[30px] w-[30px]"
                    @click="swipeNext" />
            </div>

            <!-- 无主播报名的缺省状态 -->
            <div
                v-else
                class="bg-default h-[260.5px] w-[236px] flex flex-col items-center px-5 py-15 text-center"
                :style="{ backgroundImage: `url(${requireImg('tab2/<EMAIL>')})` }"
            >
                <div class="mt-5">
                    <img
                        :src="requireImg('tab2/<EMAIL>')"
                        alt="暂无主播"
                        class="h-[63px] w-[63px]" />
                </div>
                <div class="mt-53 h-40 flex-shrink-0">
                    <div class="text-14 text-[#DFDFDF]">参演嘉宾正在赶来路上~</div>
                    <div class="text-14 text-[#DFDFDF]">敬请期待噢</div>
                </div>
            </div>
        </div>
        <div
            class="bg-default relative mt-12 h-[66px] w-[350px] flex items-center pl-70 pr-16"
            :style="{ backgroundImage: `url(${requireImg('tab2/<EMAIL>')})` }"
        >
            <img
                class="absolute left-17 top-13 h-[42px] w-[42px]"
                src="@/assets/img/tab2/<EMAIL>"
                alt="">
            <img
                class="absolute left-11 top-[-5.5px] h-[18.5px] w-[55px]"
                src="@/assets/img/tab2/<EMAIL>"
                alt="">

            <div class="text-14 text-[#FFFFFF]">
                <div>
                    每日收听5min官频巡演可得
                </div>
                <div class="mt-2 text-13 text-[#F09E56]">
                    {{ getRewardDataInfo('A6').name }}麦位框*1天
                </div>
            </div>
            <div class="ml-auto">
                <img
                    v-if="tourStore.rewardStatus === 0"
                    class="h-[31.5px] w-[62px]"
                    src="@/assets/img/tab2/<EMAIL>"
                    alt="">
                <img
                    v-else-if="tourStore.rewardStatus === 1"
                    class="h-[31.5px] w-[62px]"
                    src="@/assets/img/tab2/<EMAIL>"
                    @click="tourStore.handleReserve">
                <img
                    v-else-if="tourStore.rewardStatus === 2"
                    class="h-[16px] w-[57.5px]"
                    src="@/assets/img/tab2/<EMAIL>"
                    alt="">
            </div>
        </div>
    </div>
</template>

<script setup name="Tab2">
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import useTourStore from './use-tour-store';
import { getRewardDataInfo } from '@/utils';
import useInitStore from '@/stores/modules/use-init-store';
import { jumpRandomRoom } from '@/utils/jsbridge';

dayjs.extend(duration);

const initStore = useInitStore();
const tourStore = useTourStore();

// 活动状态：0-未开始，1-进行中，2-已结束但在当天23:59:59前
const activityStatus = computed(() => {
    const curTime = dayjs.unix(initStore.serverTime).tz('Asia/Shanghai');
    const day2000 = curTime.clone().set('hour', 20).set('minute', 0).set('second', 0);
    const day2230 = curTime.clone().set('hour', 22).set('minute', 30).set('second', 0);
    const dayEnd = curTime.clone().set('hour', 23).set('minute', 59).set('second', 59);

    if (curTime.isBefore(day2000)) {
        return 0; // 未开始
    }
    else if (curTime.isSameOrAfter(day2000) && curTime.isBefore(day2230)) {
        return 1; // 进行中
    }
    else if (curTime.isSameOrAfter(day2230) && curTime.isSameOrBefore(dayEnd)) {
        return 2; // 已结束但在当天
    }
    return 0; // 其他情况按未开始处理
});

// 活动状态标识文本
const activityStatusText = computed(() => {
    switch (activityStatus.value) {
        case 1:
            return '活动进行中';
        case 2:
            return '今日活动已结束';
        default:
            return '';
    }
});

function jumpOfficeRoom(params) {
    toRoom(initStore?.initData?.gfCid);
}

// 新的倒计时逻辑
const remainTimeStr = computed(() => {
    const curTime = dayjs.unix(initStore.serverTime).tz('Asia/Shanghai');

    if (activityStatus.value === 0) {
        // 未开始：显示距离20:30:00的倒计时
        const day2000 = curTime.clone().set('hour', 20).set('minute', 30).set('second', 0);
        const diff = day2000.diff(curTime, 'second');
        if (diff <= 0) {
            tourStore.fetchTourList();
        }
        return dayjs.duration(Math.max(0, diff), 'second').format('HH:mm:ss');
    }
    else if (activityStatus.value === 2) {
        // 已结束但在当天：显示距离下一天20:30:00的倒计时
        const nextDay2000 = curTime.clone().add(1, 'day').set('hour', 20).set('minute', 30).set('second', 0);
        const diff = nextDay2000.diff(curTime, 'second');
        return dayjs.duration(Math.max(0, diff), 'second').format('HH:mm:ss');
    }

    // 活动进行中不显示倒计时
    return '';
});

const {
    currentSchedule,
    currentAnchorId,
    hasAnchors,
} = storeToRefs(tourStore);

// 轮播组件引用
const swipeRef = ref(null);

// 移除未使用的 countdownDisplay，因为我们现在使用新的倒计时逻辑

// 当前轮播索引
const currentSwipeIndex = ref(0);

/**
 * 初始化数据
 */
async function initTourData() {
    try {
        await tourStore.fetchTourList();

        // 如果是进行中状态，定位到当前表演的主播
        if (currentAnchorId.value) {
            await nextTick();
            locateCurrentAnchor();
        }
    }
    catch (error) {
        console.error('初始化巡演数据失败:', error);
    }
}

/**
 * 定位到当前表演的主播
 */
function locateCurrentAnchor() {
    if (!currentAnchorId.value || !swipeRef.value)
        return;

    const anchorIndex = currentSchedule.value.anchors.findIndex(
        anchor => anchor?.userInfo?.uid === currentAnchorId.value,
    );

    if (anchorIndex !== -1) {
        swipeRef.value.swipeTo(anchorIndex);
        currentSwipeIndex.value = anchorIndex;
    }
}

/**
 * 轮播切换事件
 */
function onSwipeChange(index) {
    currentSwipeIndex.value = index;
}

/**
 * 手动切换到上一张
 */
function swipePrev() {
    if (swipeRef.value) {
        swipeRef.value.prev();
    }
}

/**
 * 手动切换到下一张
 */
function swipeNext() {
    if (swipeRef.value) {
        swipeRef.value.next();
    }
}

/**
 * 关注按钮点击
 */
async function onFollowClick(anchor) {
    if (!anchor.isFollow) {
        await tourStore.handleFollowAnchor(anchor.userInfo?.uid);
    }
    // 如果已关注，暂时不处理取消关注逻辑
}

/**
 * 去收听按钮点击
 */
function onListenClick(anchor) {
    tourStore.goToAnchorRoom(anchor.roomId);
}

// 组件挂载时初始化数据
onMounted(() => {
    initTourData();
});

// 组件激活时刷新数据
onActivated(() => {
    initTourData();
});

// 监听当前主播变化，自动定位
// watch(currentAnchorId, () => {
//     if (currentScheduleStatus.value === 1) {
//         nextTick(() => {
//             locateCurrentAnchor();
//         });
//     }
// });
</script>

<style lang="less">
.room-status-anchor {
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
}
</style>
