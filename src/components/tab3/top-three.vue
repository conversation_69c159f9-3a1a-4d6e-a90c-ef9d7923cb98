<template>
    <div class="tab4-top-three">
        <div class="podium">
            <!-- 第二名 -->
            <div
                v-if="secondPlace"
                class="rank-item">
                <div class="avatar-container">
                    <img
                        :src="getAvatar(secondPlace.username)"
                        class="avatar"
                    />
                </div>
                <div class="rank-2 absolute left-0 top-0 flex flex-col items-center">
                    <div
                        class="avatar-container"
                        @click="toPerson(secondPlace.username)"
                    >
                        <room-status-anchor
                            v-if="secondPlace.channelInfo?.channelId"
                            :cid="secondPlace.channelInfo?.channelId"
                            :status="secondPlace.channelInfo?.status"
                            class="room-status-anchor" />
                    </div>
                    <div class="nickname">{{ secondPlace.nickname }}</div>
                    <div class="value">
                        <img
                            class="mr-2 h-16 w-16"
                            src="@/assets/img/tab3/<EMAIL>"
                            alt="">{{ formatValue(secondPlace.value) }}
                    </div>
                </div>
            </div>

            <!-- 第一名 -->
            <div
                v-if="firstPlace"
                class="rank-item top-1 mx-[-10px]">
                <div class="avatar-container">
                    <img
                        :src="getAvatar(firstPlace.username)"
                        class="avatar"
                    />
                </div>
                <div class="rank-1 absolute left-0 top-0 flex flex-col items-center pt-35">
                    <div
                        class="avatar-container"
                        @click="toPerson(firstPlace.username)"
                    >
                        <room-status-anchor
                            v-if="firstPlace.channelInfo?.channelId"
                            :cid="firstPlace.channelInfo?.channelId"
                            :status="firstPlace.channelInfo?.status"
                            class="room-status-anchor" />
                    </div>
                    <div class="nickname">{{ firstPlace.nickname }}</div>
                    <div class="value">
                        <img
                            class="mr-2 h-16 w-16"
                            src="@/assets/img/tab3/<EMAIL>"
                            alt="">{{ formatValue(firstPlace.value) }}
                    </div>
                </div>
            </div>

            <!-- 第三名 -->
            <div
                v-if="thirdPlace"
                class="rank-item mx-10">
                <div class="avatar-container">
                    <img
                        :src="getAvatar(thirdPlace.username)"
                        class="avatar"
                        @click="toPerson(thirdPlace.username)" />
                </div>
                <div class="rank-3 absolute left-0 top-0 flex flex-col items-center">
                    <div
                        class="avatar-container"
                        @click="toPerson(thirdPlace.username)"
                    >
                        <room-status-anchor
                            v-if="thirdPlace.channelInfo?.channelId"
                            :cid="thirdPlace.channelInfo?.channelId"
                            :status="thirdPlace.channelInfo?.status"
                            class="room-status-anchor" />
                    </div>
                    <div class="nickname">{{ thirdPlace.nickname }}</div>
                    <div class="value">
                        <img
                            class="mr-2 h-16 w-16"
                            src="@/assets/img/tab3/<EMAIL>"
                            alt="">{{ formatValue(thirdPlace.value) }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { formatNumber } from '@/utils';

const props = defineProps({
    list: {
        type: Array,
        default: () => [],
    },
    type: {
        type: Number,
        required: true,
    },
});

const firstPlace = computed(() => props.list[0] || null);
const secondPlace = computed(() => props.list[1] || null);
const thirdPlace = computed(() => props.list[2] || null);

function formatValue(value) {
    return formatNumber(value || 0);
}
</script>

<style lang="less" scoped>
.tab4-top-three {
    .podium {
        display: flex;
        justify-content: center;
        align-items: flex-end;
        padding: 10px 0;

        .rank-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            width: 125.5px;
            height: 173px;
            padding-top: 35px;

            .avatar-container {
                position: relative;
                width: 58px;
                height: 58px;
                .avatar {
                    border-radius: 50%;
                    width: 58px;
                    height: 58px;
                }
            }
            &.top-1 {
                width: 139.5px;
                height: 192px;
            }
            .nickname {
                margin-top: 34px;
                font-size: 14px;
                line-height: 14px;
                color: #fff;
                max-width: 80px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                text-align: center;
            }

            .value {
                width: 91.5px;
                height: 20px;
                background: #555069;
                border-radius: 5px;
                border-radius: 5px;
                margin-top: 6px;
                font-size: 14px;
                color: #ffffff;
                text-align: center;
                display: flex;
                justify-content: center;
                align-items: center;
            }

            // 第一名样式
            .rank-1 {
                background-image: url('@/assets/img/tab3/<EMAIL>');
                background-size: 100%;
                width: 139.5px;
                height: 192px;
                padding-top: 35px;

                .avatar {
                    width: 58px;
                    height: 58px;
                    background: #000000;
                }

                .nickname {
                    font-size: 14px;
                }
                .value {
                    width: 101px;
                }
            }

            // 第二名样式
            .rank-2 {
                background-image: url('@/assets/img/tab3/<EMAIL>');
                background-size: 100%;
                width: 125.5px;
                height: 173px;
                padding-top: 30px;

                .avatar {
                    width: 58px;
                    height: 58px;
                }
            }

            // 第三名样式
            .rank-3 {
                background-image: url('@/assets/img/tab3/<EMAIL>');
                background-size: 100%;
                width: 125.5px;
                height: 173px;
                padding-top: 30px;
                .avatar {
                    width: 58px;
                    height: 58px;
                }
            }
        }
    }
}
.room-status-anchor {
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
}
</style>
