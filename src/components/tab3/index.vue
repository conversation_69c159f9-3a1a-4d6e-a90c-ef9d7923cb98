<template>
    <div
        ref="scrollContainer"
        class="flex flex-col items-center bg-[#141721] pb-160"
    >
        <!-- 子导航 -->
        <sub-nav-tabs
            v-model="rankStore.currentType"
            class="pb-13"
            :nav-config="navConfig"
            @change="handleTypeChange"
        />

        <div
            v-if="rankStore.currentType === RANK_TYPE.TAB1"
            class="bg-default relative h-[452.5px] w-[375px] flex flex-col items-center"
            :style="{ backgroundImage: `url(${requireImg('tab3/<EMAIL>')})` }"
        >
            <div class="h-[12px] pt-7 text-left text-[12px] text-[#ffebc7] leading-[12px]">累计多日参与星动挑战打分可得奖励</div>

            <!-- 任务进度区域 -->
            <div class="relative w-[320px] flex">
                <div
                    class="bg-default z-1 mt-[14px] h-[95.5px] w-[52px] flex flex-col items-center"
                    :style="{ backgroundImage: `url(${requireImg('tab3/<EMAIL>')})` }"
                >
                    <img
                        class="h-[51px] w-[51px] border-[2px] border-[#dad1ff] rounded-full bg-[#aca6c1]"
                        :src="getAvatar(audioPlayer?.user?.username)"
                        alt="">
                    <div class="mt-4 text-12 text-[#FFEF67] leading-12"> <span class="text-15">{{ audioPlayer?.value }}</span> 天</div>
                    <div class="mt-6 text-[11px] text-[#ADA5CC] leading-11">已参与</div>
                </div>
                <ChallengeProgress
                    class="ml-[-8px] mt-[56px]"
                    :value="audioPlayer.value"
                    :done-icon="requireImg('tab3/<EMAIL>')"
                    :un-done-icon="requireImg('tab3/<EMAIL>')"
                    :targets="[
                        {
                            value: 1,
                            rewards: [{ ...getRewardDataInfo('A24'), num: 1 }],
                        },
                        {
                            value: 3,
                            rewards: [{ ...getRewardDataInfo('A25'), num: 3 }],
                        },
                        {
                            value: 5,
                            rewards: [{ ...getRewardDataInfo('A26'), num: 1 }],

                        },
                    ]"
                />
            </div>

            <!-- 打分区域 -->
            <div class="mt-6 w-100% flex-1">
                <!-- 主播信息卡片 -->
                <div class="flex flex-col items-center">
                    <!-- 主播头像和信息 -->
                    <div class="relative mb-3 flex flex-col items-center">
                        <div
                            class="flex-center relative h-[100px] w-[100px] flex overflow-hidden border-[2px] border-[#ff89e7] rounded-full border-solid bg-[#f2ceeb]"
                        >
                            <img
                                :class="{ glass: !showAnchorInfo }"
                                :src="getAvatar(audioPlayer.anchorWork?.anchor?.username) "
                                class="h-98 w-98 rounded-full"
                                :alt="showAnchorInfo ? audioPlayer.anchorWork?.anchor?.nickname : '神秘主播'"
                                @click="toPerson(audioPlayer.anchorWork?.anchor?.username)"
                            />
                            <img
                                v-if="!showAnchorInfo"
                                class="absolute left-0 top-0 h-full w-full rounded-full"
                                src="@/assets/img/tab3/<EMAIL>"
                                alt="">
                        </div>
                        <div
                            :class="{ glass: !showAnchorInfo }"
                            class="mt-10 text-[15px] text-[#FFFFFF] font-bold">
                            {{ showAnchorInfo ? safeOmitTxt(audioPlayer.anchorWork?.anchor?.nickname) : '神秘主播' }}
                        </div>
                        <div
                            :class="{ glass: !showAnchorInfo }"
                            class="line-clamp-2 mt-12 h-30 w-220 text-center text-[11px] text-[#C9C9C9] leading-14">
                            {{ audioPlayer.anchorWork?.introduction }}
                        </div>
                    </div>

                    <!-- 音频播放器 -->
                    <div
                        class="bg-default relative mt-5 h-[28px] w-[243.5px]"
                        :style="{ backgroundImage: `url(${requireImg('tab3/<EMAIL>')})` }"
                    >
                        <div
                            v-if="audioPlayer.duration"
                            class="absolute left-20 top-9 text-[12px] text-[#C587CB]">
                            {{ Math.floor(audioPlayer.duration) }}”
                        </div>
                        <img
                            class="absolute left-68 top-6 h-[16.5px] w-[120px]"
                            src="@/assets/img/tab3/<EMAIL>"
                            alt="">
                        <!-- 播放按钮 -->
                        <div
                            class="absolute right-[-8px] top-[-5px] h-[38px] w-[37.5px]"
                            @click="handlePlayToggle"
                        >
                            <img
                                v-if="audioPlayer.playing"
                                src="@/assets/img/tab3/<EMAIL>"
                                class="h-full w-full"
                                alt="">
                            <img
                                v-else
                                class="h-full w-full"
                                src="@/assets/img/tab3/<EMAIL>"
                                alt="">
                        </div>
                    </div>
                    <img
                        v-if="!audioPlayer.notPlay"
                        class="mt-[12px] h-[43.5px] w-[165.5px]"
                        src="@/assets/img/tab3/<EMAIL>"
                        alt=""
                        @click="handlePlayToggle">
                    <!-- 评分区域 -->
                    <div
                        v-else
                        class="mt-10 w-100% flex flex-col items-center">
                        <!-- 评分条 -->
                        <div class="mb-3 flex items-center justify-between">
                            <div
                                v-for="score in 5"
                                :key="score"
                                class="h-[30px] w-[50px] flex cursor-pointer items-center justify-center"
                                @click="handleScoreSelect(score)"
                            >
                                <img
                                    v-if="selectedScore < score"
                                    class="h-[33px] w-[33px]"
                                    src="@/assets/img/tab3/<EMAIL>"
                                    alt="">
                                <img
                                    v-else
                                    class="h-[33px] w-[33px]"
                                    src="@/assets/img/tab3/<EMAIL>"
                                    alt="">
                            </div>
                        </div>

                        <div
                            v-if="!hasScored"
                            class="bg-default bg-default align-center absolute left-1/2 top-405 h-[17.5px] w-[112px] flex justify-center pt-4 text-12 text-[#FFD799] leading-12 -translate-x-1/2"
                            :style="{ backgroundImage: `url(${requireImg('tab3/<EMAIL>')})` }"
                        >
                            {{ countdown > 0 ? `${countdown}s后开始` : '现可进行' }}打分
                        </div>
                    </div>

                    <!-- 打分结果 -->
                    <template
                        v-if="hasScored"
                    >
                        <div
                            v-if="audioPlayer.notLike"
                            class="bg-default absolute left-3 top-145 h-[45px] w-[130px] pl-11 pr-11 pt-8 text-10 text-[#FFFFFF] leading-13"
                            :style="{ backgroundImage: `url(${requireImg('tab3/<EMAIL>')})` }"
                        >
                            看来你们的磁场并不太契合呢,看看下一个吧~
                        </div>
                        <div
                            v-else
                            class="bg-default absolute left-16 top-145 h-[45px] w-[105px] pl-11 pr-13 pt-8 text-11 text-[#FFFFFF] leading-16"
                            :style="{ backgroundImage: `url(${requireImg('tab3/<EMAIL>')})` }"
                        >
                            怦然星动!那就彼此认识一下吧！
                        </div>
                        <template v-if="!audioPlayer.notLike && hasScored">
                            <div
                                v-if="audioPlayer.anchorWork?.channelId"
                                class="align-center absolute left-1/2 top-224 h-[24px] w-[82.5px] flex items-center justify-center rounded-[12px] bg-[#ff007e] pr-4 text-17 text-[#FFFFFF] -translate-1/2"
                                @click="handleGoToRoom">
                                看看TA
                                <div
                                    v-if="nextDuration"
                                    class="absolute right-3 top-8 text-10 text-[#fff]">
                                    {{ nextDuration }}s
                                </div>
                                <img
                                    class="breath absolute left-70 h-[41px] w-[41px] -top-1"
                                    src="@/assets/img/tab3/<EMAIL>"
                                    alt="">
                            </div>
                            <div
                                v-else
                                class="align-center absolute left-1/2 top-224 h-[24px] w-[82.5px] flex items-center justify-center rounded-[12px] bg-[#ff007e] pr-4 text-17 text-[#FFFFFF] -translate-1/2"
                                @click="audioPlayer.handleFollow">
                                {{ (audioPlayer?.isFollow) ? '已关注' : '关注TA' }}
                                <div
                                    v-if="nextDuration"
                                    class="absolute right-3 top-8 text-10 text-[#fff]">
                                    {{ nextDuration }}s
                                </div>
                                <img
                                    class="breath absolute left-70 h-[41px] w-[41px] -top-1"
                                    src="@/assets/img/tab3/<EMAIL>"
                                    alt="">
                            </div>
                        </template>
                        <div
                            class="bg-default absolute right-10 top-134 h-[30px] w-[72px] flex items-center pl-50 text-13 text-[#FFFFFF]"
                            :style="{ backgroundImage: `url(${requireImg('tab3/<EMAIL>')})` }"
                            @click="handleNextChallenge"
                        >
                            <div v-if="audioPlayer.notLike">
                                {{ nextDuration }}s
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </div>
        <!-- 榜单内容 -->
        <div
            v-if="rankStore.currentType === RANK_TYPE.TAB2"
            class="rank-content">
            <div
                class="bg-default mx-auto h-[70.5px] w-[350px] flex items-center justify-center px-17"
                :style="{ backgroundImage: `url(${requireImg('tab3/<EMAIL>')})` }"
            >
                <div class="text-[11px] text-[#D8D8D8] leading-1.5">
                    <div class="mb-10 text-[12px] text-[#FFEBC7]">TOP10达人将获得【怦然是动达人】大v认证</div>
                    <div class="flex items-center">
                        按达人累计收到的星动指数<img
                            class="ml-2 h-16 w-16"
                            src="@/assets/img/tab3/<EMAIL>"
                            alt="">进行排名
                    </div>
                    <div class="mt-6">每个用户每天仅统计前5次打分</div>
                </div>
                <div
                    class="bg-default ml-auto h-[47.5px] w-[47.5px] flex items-center justify-center"
                    :style="{ backgroundImage: `url(${requireImg('tab3/<EMAIL>')})` }"
                >
                    <img
                        class="h-[40px] w-[40px]"
                        src="@/assets/img/tab3/<EMAIL>"
                        alt="">
                </div>
            </div>
            <!-- 错误状态 -->
            <div
                v-if="rankStore.error.hasError"
                class="error-state">
                <div class="error-icon">⚠️</div>
                <div class="error-message">{{ rankStore.error.message }}</div>
                <div
                    v-if="rankStore.error.canRetry"
                    class="retry-btn"
                    @click="handleRetry"
                >
                    重新加载
                </div>
            </div>

            <!-- 正常内容 -->
            <template v-else>
                <!-- 前三名 -->
                <top-three
                    v-if="rankStore.topThreeList.length > 0"
                    :list="rankStore.topThreeList"
                    :type="rankStore.currentType"
                />

                <!-- 普通排名列表 -->
                <div
                    v-if="rankStore.normalList.length > 0"
                    class="normal-list">
                    <rank-item
                        v-for="(item, index) in rankStore.normalList"
                        :key="`${rankStore.currentType}-${item.uid || item.id}-${index}`"
                        :item="item"
                        :rank="index + 4"
                        :type="rankStore.currentType"
                    />
                </div>

                <!-- 自己的排名 -->
                <div
                    v-if="rankStore.hasSelfRank"
                    class="self-rank">
                    <rank-item
                        :item="rankStore.selfRank"
                        :rank="rankStore.selfRank.rank || rankStore.selfRank.ranking"
                        :type="rankStore.currentType"
                        :is-self="true"
                    />
                </div>

                <!-- 空状态 -->
                <div
                    v-if="rankStore.showEmptyState"
                    class="empty-state">
                    <div class="empty-text">暂无榜单数据</div>
                </div>

                <!-- 加载更多提示 -->
                <div
                    v-if="rankStore.loading.more"
                    class="loading-more">
                    <div class="loading-spinner"></div>
                    <span>加载中...</span>
                </div>

                <!-- 没有更多数据 -->
                <div
                    v-if="!rankStore.hasMore && rankStore.list.length > 0"
                    class="no-more">
                    <div class="no-more-line"></div>
                    <span>没有更多数据了</span>
                    <div class="no-more-line"></div>
                </div>
            </template>
        </div>

        <common-copyright></common-copyright>
    </div>
</template>

<script setup>
import { throttle } from 'lodash-es';
import SubNavTabs from './sub-nav-tabs.vue';
import ChallengeProgress from './challenge-progress.vue';
import useRankStore, { RANK_TYPE } from './rank-store';
import useAudioPlayer from './use-audio-player';
import useLoading from '@/hooks/use-loading';
import { getRewardDataInfo } from '@/utils';
import useFollow from '@/use/use-follow';
import { toRoom } from '@/utils/jsbridge';

const rankStore = useRankStore();
const scrollContainer = ref(null);
const audioPlayer = useAudioPlayer();

// 打分状态
const selectedScore = ref(0);
const hasScored = ref(false);
const nextDuration = ref(0);

// 倒计时基于音频播放时长计算
const countdown = computed(() => {
    const requiredSeconds = 10; // 需要播放10秒才能打分
    return Math.max(0, requiredSeconds - audioPlayer.secondsSum);
});

// 是否可以开始播放
const canStartPlaying = computed(() => true);

// 是否显示主播信息
const showAnchorInfo = computed(() => hasScored.value);

// 是否可以提交评分
const canSubmitScore = computed(() => selectedScore.value > 0 && countdown.value === 0 && !hasScored.value);

// 导航配置
const navConfig = computed(() => [
    {
        value: RANK_TYPE.TAB1,
        label: '星动挑战',
    },
    {
        value: RANK_TYPE.TAB2,
        label: '星动指数榜',
    },
]);

const isPause = ref(false);

// 播放/暂停切换
function handlePlayToggle() {
    if (!canStartPlaying.value)
        return;

    if (audioPlayer.playing) {
        console.log('暂停');

        audioPlayer.pauseAudio();
        isPause.value = true;
    }
    else {
        if (isPause.value) {
            audioPlayer.continueAudio();
            isPause.value = false;
            return;
        }
        // 这里需要音频URL，从audioPlayer.anchorWork.workLink获取
        if (audioPlayer.anchorWork?.workLink) {
            isPause.value = false;
            audioPlayer.playAudio(audioPlayer.anchorWork.workLink);
        }
    }
}

// 选择评分
async function handleScoreSelect(score) {
    if (countdown.value > 0)
        return;
    selectedScore.value = score;
    if (!canSubmitScore.value)
        return;

    try {
        await audioPlayer.rate(selectedScore.value);
        hasScored.value = true;

        // 如果评分不喜欢，启动3秒定时器自动跳转到下一个
        if (audioPlayer.notLike) {
            createNextDurationTimer(() => {
                handleNextChallenge();
            });
        }
        else {
            createNextDurationTimer(() => {
                if (audioPlayer.anchorWork?.channelId) {
                    handleGoToRoom();
                }
                else {
                    audioPlayer.handleFollow();
                }
            });
        }
    }
    catch (error) {
        console.error('提交评分失败:', error);
    }
}
// 添加一个nextDuration 的定时器，3s后执行传入的函数任务
const nextTimer = ref(null);

/**
 * 创建nextDuration定时器，3秒后执行传入的函数任务
 * @param {Function} callback - 3秒后要执行的回调函数
 */
function createNextDurationTimer(callback) {
    // 清除之前的定时器
    if (nextTimer.value) {
        clearInterval(nextTimer.value);
        nextTimer.value = null;
    }

    // 初始化倒计时为3秒
    nextDuration.value = 3;

    // 创建定时器，每秒更新倒计时
    nextTimer.value = setInterval(() => {
        nextDuration.value--;

        // 倒计时结束时执行回调函数并清除定时器
        if (nextDuration.value <= 0) {
            clearInterval(nextTimer.value);
            nextTimer.value = null;

            // 执行传入的回调函数
            if (typeof callback === 'function') {
                callback();
            }
        }
    }, 1000);
}

/**
 * 清除nextDuration定时器
 */
function clearNextDurationTimer() {
    if (nextTimer.value) {
        clearInterval(nextTimer.value);
        nextTimer.value = null;
        nextDuration.value = 0;
    }
}

// 下一个挑战
async function handleNextChallenge() {
    // 清除定时器
    clearNextDurationTimer();

    // 重置状态
    selectedScore.value = 0;
    hasScored.value = false;
    nextDuration.value = 0;

    // 获取新的挑战数据
    const code = await audioPlayer.getNewWorker();
    if (code === 0) {
        setTimeout(() => {
            if (audioPlayer.anchorWork?.workLink) {
                audioPlayer.playAudio(audioPlayer.anchorWork.workLink);
            }
        }, 1000);
    }
}

// 进入直播间
function handleGoToRoom() {
    if (audioPlayer.anchorWork?.channelId) {
        toRoom(audioPlayer.anchorWork.channelId);
    }
}

// 切换榜单类型
const handleTypeChange = async (type) => {
    await rankStore.switchRankType(type);
    if (type === RANK_TYPE.TAB2) {
        clearNextDurationTimer();
        // 重置状态
        selectedScore.value = 0;
        hasScored.value = false;
        nextDuration.value = 0;
        audioPlayer.stopAudio();
        return;
    }
    if (type === RANK_TYPE.TAB1) {
        await audioPlayer.getNewWorker();
    }
};

// 重试加载
const handleRetry = async () => {
    await rankStore.retryQuery();
};

// 滚动加载更多
const handleScroll = throttle(() => {
    rankStore.loadMore();
}, 300);

useEventBus('scroll-to-bottom').on(() => {
    console.log('下一页');
    handleScroll();
});

onMounted(async () => {
    rankStore.switchRankType(RANK_TYPE.TAB1);
    await rankStore.retryQuery();

    // 获取星动挑战数据
    await audioPlayer.getNewWorker();
});

// 组件卸载时清理定时器
onUnmounted(() => {
    clearNextDurationTimer();
    audioPlayer.stopAudio();
});
</script>

<style lang="less" scoped>
.rank-list {
    height: 100%;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-sizing: border-box;
    // background: url('@/assets/img/<EMAIL>') #244f8e no-repeat center top;
    width: 375px;
    background: #242031;
    min-height: 449px;
    padding-top: 12px;

    .rank-content {
        margin-top: 15px;

        .error-state {
            text-align: center;
            padding: 60px 20px;

            .error-icon {
                font-size: 48px;
                margin-bottom: 15px;
            }

            .error-message {
                font-size: 14px;
                color: rgba(255, 255, 255, 0.8);
                margin-bottom: 20px;
                line-height: 1.4;
            }

            .retry-btn {
                display: inline-block;
                padding: 10px 20px;
                background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
                color: #fff;
                border-radius: 20px;
                font-size: 14px;
                cursor: pointer;
                transition: all 0.3s ease;

                &:active {
                    transform: scale(0.95);
                    background: linear-gradient(135deg, #ff5252, #ff7979);
                }
            }
        }

        .normal-list {
            margin-top: 0px;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;

            .empty-icon {
                font-size: 48px;
                margin-bottom: 15px;
            }

            .empty-text {
                font-size: 16px;
                color: #fff;
                margin-bottom: 8px;
            }

            .empty-tip {
                font-size: 12px;
                color: rgba(255, 255, 255, 0.6);
            }
        }

        .loading-more {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 15px;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.6);
            gap: 8px;

            .loading-spinner {
                width: 16px;
                height: 16px;
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-top: 2px solid rgba(255, 255, 255, 0.6);
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }
        }

        .no-more {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.4);
            gap: 15px;

            .no-more-line {
                flex: 1;
                height: 1px;
                background: rgba(255, 255, 255, 0.1);
            }
        }
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.glass {
    -moz-filter: blur(5px);
    -webkit-filter: blur(5px);
    -o-filter: blur(5px);
    -ms-filter: blur(5px);
    filter: blur(5px);
}
</style>
